"""
Database operations module.
Handles connections to SQL Server and data operations.
"""

import pyodbc
from typing import List, Dict, Any, Optional
from .config import Config


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self, config: Config):
        self.config = config
        self.connection = None

    def connect(self) -> bool:
        """
        Establish connection to SQL Server database.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Get connection string from config
            conn_str = self.config.get_db_connection_string()
            
            # Create pyodbc connection
            self.connection = pyodbc.connect(conn_str)
            
            return True
        except pyodbc.Error as e:
            print(f"Database connection error: {e}")
            self.connection = None
            return False
        except Exception as e:
            print(f"Unexpected error during database connection: {e}")
            self.connection = None
            return False

    def disconnect(self):
        """
        Close database connection safely.
        """
        try:
            if self.connection:
                self.connection.close()
        except Exception as e:
            print(f"Error during disconnect: {e}")
        finally:
            self.connection = None

    def fetch_sales_notes(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Fetch sales notes from the database.
        
        TODO: Implement this method to:
        1. Execute SELECT query to get sales notes from sales_notes table
        2. Include all columns: id, note_date, client_name, note_author, note_text
        3. Apply limit if provided
        4. Return list of dictionaries with note data
        
        Args:
            limit: Optional limit on number of records to fetch
            
        Returns:
            List[Dict]: List of sales note records
        """
        # Your implementation here
        # Example structure of returned data:
        # [
        #     {
        #         'id': 1,
        #         'note_date': '2024-01-15',
        #         'client_name': 'ABC Corp',
        #         'note_author': 'John Smith',
        #         'note_text': 'Met with client to discuss requirements...'
        #     }
        # ]
        pass

    def save_processed_note(self, note_id: int, summary: str, action_items: str) -> bool:
        """
        Save processed note results back to database.
        
        TODO: Implement this method to:
        1. Create a separate results table OR add columns to sales_notes table
        2. Include the summary and action items for the given note_id
        3. Handle database errors
        4. Consider table structure for storing results
        
        Args:
            note_id: ID of the original sales note
            summary: Generated summary from OpenAI
            action_items: Extracted action items
            
        Returns:
            bool: True if save successful, False otherwise
        """
        # Your implementation here
        # Consider creating a table like:
        # CREATE TABLE processed_notes (
        #     id INT IDENTITY(1,1) PRIMARY KEY,
        #     note_id INT FOREIGN KEY REFERENCES sales_notes(id),
        #     summary NVARCHAR(MAX),
        #     action_items NVARCHAR(MAX),
        #     processed_date DATETIME DEFAULT GETDATE()
        # );
        pass

    def get_unprocessed_notes_count(self) -> int:
        """
        Get count of notes that haven't been processed yet.
        
        TODO: Implement this method to:
        1. Query to count notes that don't have corresponding processed entries
        2. This helps with progress tracking
        
        Returns:
            int: Number of unprocessed notes
        """
        # Your implementation here
        pass

    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect() 
