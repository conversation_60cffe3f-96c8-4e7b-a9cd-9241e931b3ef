# Sales Notes Processor

A Python backend tool that processes sales notes using OpenAI API to extract summaries and action items.

## 🎯 Project Overview

This tool connects to a SQL Server database, fetches sales notes, sends them to OpenAI for analysis, and saves the extracted summaries and action items back to the database.

## 📋 Your Task

You will implement the core functionality in several modules. Each module has function headers with TODO comments explaining what needs to be implemented. **Read through all the code first** to understand the overall structure.

**Important**: You'll need to design your own prompts for OpenAI to extract summaries and action items effectively!

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
# venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration Setup

**Option A: Using .env file (recommended for development)**
```bash
# Copy the example file
cp .env.example .env

# Edit .env with your actual values
# You'll need:
# - SQL Server connection details
# - OpenAI API key
```

**Option B: Using YAML configuration**
```bash
# Copy the example file
cp config.yaml.example config.yaml

# Edit config.yaml with your actual values
# Don't forget to write effective prompts for OpenAI!
```

### 3. Database Schema

Your database has a `sales_notes` table with the following structure:
```sql
CREATE TABLE sales_notes (
    id INT IDENTITY(1,1) PRIMARY KEY,
    note_date DATE,
    client_name NVARCHAR(255),
	note_author NVARCHAR(255),
    note_text NVARCHAR(MAX)
);
```

**You'll need to design how to store the processed results** (summary and action items). Consider creating a separate table or adding columns to the existing table.

## 📁 Project Structure

```
├── src/
│   ├── __init__.py          # Package marker
│   ├── config.py            # Configuration management
│   ├── database.py          # Database operations
│   ├── openai_client.py     # OpenAI API client
│   └── processor.py         # Main processing logic
├── main.py                  # Entry point script
├── requirements.txt         # Python dependencies
├── .env.example            # Environment variables template
├── config.yaml.example     # YAML configuration template
└── README.md               # This file
```

## 🛠️ Implementation Guide

### Step 1: Configuration (`src/config.py`)
- Implement `from_env()` to load from environment variables
- Implement `from_yaml()` to load from YAML file
- Implement `get_db_connection_string()` for database connection
- Implement `validate()` to check all required config is present

### Step 2: Database Operations (`src/database.py`)
- Implement `connect()` to establish SQL Server connection
- Implement `fetch_sales_notes()` to retrieve notes from the sales_notes table
- Design and implement `save_processed_note()` to store results
- Consider how to track which notes have been processed

### Step 3: OpenAI Integration (`src/openai_client.py`)
- Implement `_setup_client()` to initialize OpenAI client
- **Design effective prompts** for extracting summaries and action items
- Implement `process_sales_note()` to send notes to OpenAI
- Implement `_build_prompt()` to format prompts properly
- Implement `_parse_response()` to extract summary and action items

### Step 4: Processing Pipeline (`src/processor.py`)
- Implement `process_all_notes()` to orchestrate the entire pipeline
- Implement `process_single_note()` for individual note processing
- Implement retry logic and error handling
- Implement statistics tracking

### Step 5: Main Script (`main.py`)
- Implement argument parsing and configuration loading
- Implement the main processing flow
- Add proper error handling and logging

## 🏃‍♂️ Running the Application

```bash
# Using .env configuration (default)
python main.py

# Using YAML configuration
python main.py --config-type yaml --config-path config.yaml

# Process specific batch size
python main.py --batch-size 5

# Dry run (see what would be processed)
python main.py --dry-run
```

## 🧪 Testing Your Implementation

1. **Test Configuration Loading**
   ```python
   from src.config import Config
   
   # Test env loading
   config = Config.from_env()
   print(config.validate())
   
   # Test yaml loading
   config = Config.from_yaml()
   print(config.get_db_connection_string())
   ```

2. **Test Database Connection**
   ```python
   from src.database import DatabaseManager
   
   with DatabaseManager(config) as db:
       notes = db.fetch_sales_notes(limit=1)
       print(f"Fetched {len(notes)} notes")
   ```

3. **Test OpenAI Integration**
   ```python
   from src.openai_client import OpenAIClient
   
   client = OpenAIClient(config)
   result = client.process_sales_note("Sample sales note content")
   print(result)
   ```

## 🎯 Learning Objectives

By completing this project, you will learn:
- **Database Integration**: Connecting to SQL Server with Python
- **API Integration**: Working with external APIs (OpenAI)
- **Prompt Engineering**: Designing effective prompts for AI
- **Configuration Management**: Using environment variables and YAML
- **Error Handling**: Implementing robust error handling and retries
- **Project Structure**: Organizing code into logical modules
- **Context Managers**: Using Python's `with` statements effectively

## 🚨 Common Pitfalls to Avoid

1. **Database Connections**: Always close connections properly (use context managers)
2. **API Rate Limits**: Implement proper retry logic with backoff
3. **Error Handling**: Don't let one failed note stop the entire process
4. **Configuration**: Validate all required configuration before starting
5. **SQL Injection**: Use parameterized queries, never string concatenation
6. **Prompt Design**: Test your prompts thoroughly - bad prompts = bad results!

## 📚 Useful Resources

- [pyodbc documentation](https://pyodbc.readthedocs.io/)
- [OpenAI Python SDK](https://github.com/openai/openai-python)
- [OpenAI Prompt Engineering Guide](https://platform.openai.com/docs/guides/prompt-engineering)
- [python-dotenv documentation](https://pypi.org/project/python-dotenv/)
- [PyYAML documentation](https://pyyaml.org/wiki/PyYAMLDocumentation)

## 🤔 Need Help?

1. Read through all the function docstrings first
2. Look at the example configurations
3. Test each module independently
4. Use print statements to debug your logic
5. Check the resources above for API documentation
6. Experiment with different prompt designs

## 🎉 Success Criteria

Your implementation is successful when:
- [ ] Configuration loads from both .env and YAML files
- [ ] Database connection works and can fetch notes from sales_notes table
- [ ] OpenAI API integration processes notes correctly with your custom prompts
- [ ] Processed results (summary & action items) are saved back to database
- [ ] Error handling prevents crashes from bad data
- [ ] The main script runs end-to-end without errors
- [ ] Your prompts consistently produce useful summaries and action items

Good luck! 🍀 