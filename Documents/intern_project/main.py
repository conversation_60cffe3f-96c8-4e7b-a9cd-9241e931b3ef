#!/usr/bin/env python3
"""
Sales Notes Processor - Main Entry Point

This script processes sales notes by:
1. Fetching notes from SQL Server database
2. Sending each note to OpenAI for analysis
3. Extracting summary and action items
4. Saving results back to database
"""

import sys
import argparse
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from src.config import Config
from src.processor import SalesNotesProcessor


def main():
    """
    Main function to run the sales notes processor.
    
    TODO: Implement this function to:
    1. Parse command line arguments (config type, batch size, etc.)
    2. Load configuration (env or yaml based on argument)
    3. Validate configuration
    4. Create and run processor
    5. Handle any top-level errors
    """
    
    # Setup argument parser
    parser = argparse.ArgumentParser(description="Process sales notes with OpenAI")
    parser.add_argument(
        "--config-type", 
        choices=["env", "yaml"], 
        default="env",
        help="Configuration source: env file or yaml file"
    )
    parser.add_argument(
        "--config-path", 
        default="config.yaml",
        help="Path to YAML config file (when using --config-type yaml)"
    )
    parser.add_argument(
        "--batch-size", 
        type=int,
        help="Number of notes to process in each batch"
    )
    parser.add_argument(
        "--dry-run", 
        action="store_true",
        help="Show what would be processed without making changes"
    )
    
    args = parser.parse_args()
    
    # Your implementation here:
    # 1. Load configuration based on config_type
    # 2. Override batch_size if provided
    # 3. Create processor instance
    # 4. Run processing pipeline
    # 5. Print results
    
    print("Sales Notes Processor")
    print("====================")
    
    try:
        # Load configuration
        # TODO: Implement configuration loading
        
        # Create processor
        # TODO: Create SalesNotesProcessor instance
        
        # Run processing
        # TODO: Run the processor
        
        print("Processing completed successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 