#!/usr/bin/env python3
"""
Simple test script to validate your implementation.
Run this after implementing the functions to check if they work.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

def test_config():
    """Test configuration loading."""
    print("Testing Configuration...")
    
    try:
        from src.config import Config
        
        # Test YAML config loading
        config = Config.from_yaml("config.yaml.example")
        print("✓ YAML config loading works")
        
        # Test connection string building
        conn_str = config.get_db_connection_string()
        print("✓ Connection string building works")
        
        # Test validation
        is_valid = config.validate()
        print(f"✓ Configuration validation: {'Valid' if is_valid else 'Invalid (expected for example config)'}")
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")

def test_database():
    """Test database operations (requires valid config)."""
    print("\nTesting Database Operations...")
    
    try:
        from src.config import Config
        from src.database import DatabaseManager
        
        # Note: This will fail without valid database credentials
        # That's expected for the template
        print("ℹ Database tests require valid credentials in config")
        print("ℹ Implement the functions and test with real database")
        
    except Exception as e:
        print(f"ℹ Database test skipped (implement functions first): {e}")

def test_openai():
    """Test OpenAI client operations (requires valid API key)."""
    print("\nTesting OpenAI Integration...")
    
    try:
        from src.config import Config
        from src.openai_client import OpenAIClient
        
        # Note: This will fail without valid OpenAI API key
        # That's expected for the template
        print("ℹ OpenAI tests require valid API key in config")
        print("ℹ Implement the functions and test with real API key")
        
    except Exception as e:
        print(f"ℹ OpenAI test skipped (implement functions first): {e}")

def test_processor():
    """Test the main processor."""
    print("\nTesting Main Processor...")
    
    try:
        from src.config import Config
        from src.processor import SalesNotesProcessor
        
        config = Config.from_yaml("config.yaml.example")
        processor = SalesNotesProcessor(config)
        print("✓ Processor initialization works")
        
        # Test statistics (should work even without database)
        # stats = processor.get_processing_stats()
        # print(f"✓ Statistics retrieval works: {stats}")
        
    except Exception as e:
        print(f"✗ Processor test failed: {e}")

if __name__ == "__main__":
    print("Sales Notes Processor - Implementation Test")
    print("=" * 50)
    
    test_config()
    test_database()
    test_openai()
    test_processor()
    
    print("\n" + "=" * 50)
    print("Test completed!")
    print("\nNext steps:")
    print("1. Implement all TODO functions")
    print("2. Set up real database and OpenAI credentials")
    print("3. Run: python main.py") 